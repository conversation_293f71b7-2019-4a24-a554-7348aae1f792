services:
  vault:
    image: hashicorp/vault:latest
    container_name: vault
    environment:
      - VAULT_DEV_ROOT_TOKEN_ID=root
      - VAULT_DEV_LISTEN_ADDRESS=0.0.0.0:8200
    ports:
      - "8200:8200"
    volumes:
      - vault_data:/vault/file
    command: "server -dev"
    cap_add:
      - IPC_LOCK
    networks:
      - vault_network

  node-service:
    build: .
    container_name: node-service
    environment:
      - VAULT_ADDR=http://vault:8200
      - VAULT_TOKEN=root
      - PORT=3000
    depends_on:
      - vault
    networks:
      - vault_network
    ports:
      - "3001:3000"

volumes:
  vault_data:

networks:
  vault_network:
    driver: bridge
