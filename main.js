const express = require('express');
const axios = require('axios');
const dotenv = require('dotenv');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const path = require('path');
const AuditLogger = require('./audit-logger');

dotenv.config();

const app = express();
const port = process.env.PORT || 3000;

// Security and middleware
app.use(helmet({
    contentSecurityPolicy: {
        directives: {
            defaultSrc: ["'self'"],
            styleSrc: ["'self'", "'unsafe-inline'", "https://cdnjs.cloudflare.com"],
            scriptSrc: ["'self'"],
            fontSrc: ["'self'", "https://cdnjs.cloudflare.com"],
            imgSrc: ["'self'", "data:", "https:"],
        },
    },
}));
app.use(cors());
app.use(morgan('combined'));
app.use(express.json({ limit: '10mb' }));
app.use(express.static(path.join(__dirname, 'public')));

// Vault configuration
const VAULT_ADDR = process.env.VAULT_ADDR || 'http://vault:8200';
const VAULT_TOKEN = process.env.VAULT_TOKEN || 'root';
const API_KEY = process.env.API_KEY || '1234567890';

// Initialize audit logger
const auditLogger = new AuditLogger({
    logPath: process.env.LOG_PATH || path.join(__dirname, 'logs'),
    maxLogSize: 5 * 1024 * 1024, // 5MB
    maxLogFiles: 10
});


async function getSecret(path) {
    try {
        const response = await axios.get(`${VAULT_ADDR}/v1/${path}`, {
            headers: {
                'X-Vault-Token': VAULT_TOKEN,
            },
        });
        return response.data.data.data;
    } catch (error) {
        console.error(`Error fetching secret from Vault (${path}):`, error.message);
        throw error;
    }
}


async function setSecret(path, data, options = {}) {
    try {
        const payload = { data: data };

        // Add options for custom metadata if provided
        if (Object.keys(options).length > 0) {
            payload.options = options;
        }

        const response = await axios.post(`${VAULT_ADDR}/v1/${path}`, payload, {
            headers: {
                'X-Vault-Token': VAULT_TOKEN,
            },
        });
        return response.data;
    } catch (error) {
        console.error(`Error saving secret to Vault (${path}):`, error.message);
        throw error;
    }
}

async function deleteSecret(path) {
    try {
        const response = await axios.delete(`${VAULT_ADDR}/v1/${path}`, {
            headers: {
                'X-Vault-Token': VAULT_TOKEN,
            },
        });
        return response.data;
    } catch (error) {
        console.error(`Error deleting secret from Vault (${path}):`, error.message);
        throw error;
    }
}

async function listSecrets(path = 'secret/metadata') {
    try {
        const response = await axios.get(`${VAULT_ADDR}/v1/${path}?list=true`, {
            headers: {
                'X-Vault-Token': VAULT_TOKEN,
            },
        });
        return response.data.data.keys || [];
    } catch (error) {
        console.error(`Error listing secrets from Vault (${path}):`, error.message);
        return [];
    }
}

async function getAllSecrets() {
    try {
        const secrets = [];
        const keys = await listSecrets('secret/metadata');

        for (const key of keys) {
            try {
                const secretData = await getSecret(`secret/data/${key}`);
                const metadata = {
                    name: key,
                    path: key,
                    category: secretData.category || 'other',
                    description: secretData.description || '',
                    data: { ...secretData }
                };

                // Remove metadata fields from data
                delete metadata.data.category;
                delete metadata.data.description;

                secrets.push(metadata);
            } catch (error) {
                console.error(`Failed to fetch secret ${key}:`, error.message);
            }
        }

        return secrets;
    } catch (error) {
        console.error('Error getting all secrets:', error.message);
        return [];
    }
}

async function getSecretVersions(path) {
    try {
        const response = await axios.get(`${VAULT_ADDR}/v1/${path}/metadata`, {
            headers: {
                'X-Vault-Token': VAULT_TOKEN,
            },
        });
        return response.data.data.versions || {};
    } catch (error) {
        console.error(`Error fetching secret versions from Vault (${path}):`, error.message);
        return {};
    }
}


async function getSecretVersion(path, version) {
    try {
        const response = await axios.get(`${VAULT_ADDR}/v1/${path}?version=${version}`, {
            headers: {
                'X-Vault-Token': VAULT_TOKEN,
            },
        });
        return response.data.data.data;
    } catch (error) {
        console.error(`Error fetching secret version from Vault (${path}, v${version}):`, error.message);
        throw error;
    }
}

// Authentication middleware
const authenticateApiKey = (req, res, next) => {
    const apiKey = req.headers['x-api-key'];

    if (!apiKey || apiKey !== API_KEY) {
        // Log failed authentication
        auditLogger.log({
            action: 'authentication',
            resource: req.originalUrl,
            actor: req.ip,
            details: { method: req.method },
            outcome: 'failure'
        }).catch(err => console.error('Failed to log auth failure:', err));

        return res.status(401).json({ error: 'Unauthorized: Invalid API key' });
    }

    // Add actor info to request for audit logging
    req.actor = req.ip;
    next();
};


// API

// Get all secrets
app.get('/api/secrets', authenticateApiKey, async (req, res) => {
    try {
        const secrets = await getAllSecrets();
        res.json({ secrets });
    } catch (error) {
        console.error('Failed to fetch secrets:', error);
        res.status(500).json({ error: 'Failed to fetch secrets' });
    }
});

// Get a specific secret
app.get('/api/secrets/:path', authenticateApiKey, async (req, res) => {
    const secretPath = decodeURIComponent(req.params.path);
    try {
        const secretData = await getSecret(`secret/data/${secretPath}`);

        const secret = {
            name: secretPath,
            path: secretPath,
            category: secretData.category || 'other',
            description: secretData.description || '',
            data: { ...secretData }
        };

        // Remove metadata fields from data
        delete secret.data.category;
        delete secret.data.description;

        // Log successful access
        auditLogger.log({
            action: 'read',
            resource: `secret/data/${secretPath}`,
            actor: req.actor,
            details: {
                category: secret.category,
                fields: Object.keys(secret.data)
            }
        }).catch(err => console.error('Failed to log secret read:', err));

        res.json({ secret });
    } catch (error) {
        console.error('Failed to fetch secret:', error);

        // Log failed access
        auditLogger.log({
            action: 'read',
            resource: `secret/data/${secretPath}`,
            actor: req.actor,
            outcome: 'failure',
            details: { error: error.message }
        }).catch(err => console.error('Failed to log secret read failure:', err));

        res.status(404).json({ error: 'Secret not found' });
    }
});

// Create a new secret
app.post('/api/secrets', authenticateApiKey, async (req, res) => {
    try {
        const { name, category, description, data, expiresAt, rotationPeriod } = req.body;

        if (!name || !data || typeof data !== 'object') {
            return res.status(400).json({
                error: 'Missing required parameters: name and data are required'
            });
        }

        // Add metadata to the secret data
        const secretData = {
            ...data,
            category: category || 'other',
            description: description || '',
            created_at: new Date().toISOString()
        };

        // Add expiration date if provided
        if (expiresAt) {
            secretData.expires_at = expiresAt;
        }

        // Add rotation period if provided (in days)
        if (rotationPeriod) {
            secretData.rotation_period = rotationPeriod;

            // Calculate next rotation date
            const nextRotationDate = new Date();
            nextRotationDate.setDate(nextRotationDate.getDate() + rotationPeriod);
            secretData.next_rotation = nextRotationDate.toISOString();
        }

        await setSecret(`secret/data/${name}`, secretData);

        // Log secret creation
        auditLogger.log({
            action: 'create',
            resource: `secret/data/${name}`,
            actor: req.actor,
            details: {
                category,
                hasExpiry: !!expiresAt,
                hasRotation: !!rotationPeriod,
                fieldCount: Object.keys(data).length
            }
        }).catch(err => console.error('Failed to log secret creation:', err));

        res.status(201).json({
            message: 'Secret created successfully',
            path: name
        });
    } catch (error) {
        console.error('Failed to create secret:', error);

        // Log failed creation
        auditLogger.log({
            action: 'create',
            resource: req.body.name ? `secret/data/${req.body.name}` : 'unknown',
            actor: req.actor,
            outcome: 'failure',
            details: { error: error.message }
        }).catch(err => console.error('Failed to log secret creation failure:', err));

        res.status(500).json({ error: 'Failed to create secret' });
    }
});

// Update an existing secret
app.put('/api/secrets/:path', authenticateApiKey, async (req, res) => {
    try {
        const secretPath = decodeURIComponent(req.params.path);
        const { data } = req.body;

        if (!data || typeof data !== 'object') {
            return res.status(400).json({
                error: 'Missing required parameter: data'
            });
        }

        // Get existing secret to preserve metadata
        let existingSecret;
        try {
            existingSecret = await getSecret(`secret/data/${secretPath}`);
        } catch (error) {
            return res.status(404).json({ error: 'Secret not found' });
        }

        // Merge new data with existing metadata
        const updatedData = {
            ...data,
            category: existingSecret.category || 'other',
            description: existingSecret.description || ''
        };

        await setSecret(`secret/data/${secretPath}`, updatedData);
        res.json({
            message: 'Secret updated successfully',
            path: secretPath
        });
    } catch (error) {
        console.error('Failed to update secret:', error);
        res.status(500).json({ error: 'Failed to update secret' });
    }
});

// Delete a secret
app.delete('/api/secrets/:path', authenticateApiKey, async (req, res) => {
    try {
        const secretPath = decodeURIComponent(req.params.path);
        await deleteSecret(`secret/data/${secretPath}`);
        res.json({
            message: 'Secret deleted successfully',
            path: secretPath
        });
    } catch (error) {
        console.error('Failed to delete secret:', error);
        res.status(500).json({ error: 'Failed to delete secret' });
    }
});

// Get secret version history
app.get('/api/secrets/:path/versions', authenticateApiKey, async (req, res) => {
    try {
        const secretPath = decodeURIComponent(req.params.path);
        const versions = await getSecretVersions(`secret/metadata/${secretPath}`);

        const versionData = Object.entries(versions).map(([version, data]) => ({
            version: parseInt(version),
            created_time: data.created_time,
            deleted: data.deletion_time ? true : false,
            destroyed: data.destroyed || false
        })).sort((a, b) => b.version - a.version); // Sort by version descending

        res.json({
            path: secretPath,
            versions: versionData
        });
    } catch (error) {
        console.error('Failed to fetch secret versions:', error);
        res.status(500).json({ error: 'Failed to fetch secret versions' });
    }
});

// Get a specific version of a secret
app.get('/api/secrets/:path/versions/:version', authenticateApiKey, async (req, res) => {
    try {
        const secretPath = decodeURIComponent(req.params.path);
        const version = parseInt(req.params.version);

        if (isNaN(version) || version <= 0) {
            return res.status(400).json({ error: 'Invalid version number' });
        }

        const secretData = await getSecretVersion(`secret/data/${secretPath}`, version);

        const secret = {
            name: secretPath,
            path: secretPath,
            version: version,
            category: secretData.category || 'other',
            description: secretData.description || '',
            data: { ...secretData }
        };

        // Remove metadata fields from data
        delete secret.data.category;
        delete secret.data.description;

        res.json({ secret });
    } catch (error) {
        console.error('Failed to fetch secret version:', error);
        res.status(404).json({ error: 'Secret version not found' });
    }
});

// Export all secrets
app.get('/api/export', authenticateApiKey, async (req, res) => {
    try {
        const secrets = await getAllSecrets();
        const exportData = {
            exportDate: new Date().toISOString(),
            version: '2.0.0',
            secrets: secrets
        };
        res.json(exportData);
    } catch (error) {
        console.error('Failed to export secrets:', error);
        res.status(500).json({ error: 'Failed to export secrets' });
    }
});

// Get secret version history
app.get('/api/secrets/:path/versions', authenticateApiKey, async (req, res) => {
    try {
        const secretPath = decodeURIComponent(req.params.path);
        const versions = await getSecretVersions(`secret/metadata/${secretPath}`);

        const versionData = Object.entries(versions).map(([version, data]) => ({
            version: parseInt(version),
            created_time: data.created_time,
            deleted: data.deletion_time ? true : false,
            destroyed: data.destroyed || false
        })).sort((a, b) => b.version - a.version); // Sort by version descending

        res.json({
            path: secretPath,
            versions: versionData
        });
    } catch (error) {
        console.error('Failed to fetch secret versions:', error);
        res.status(500).json({ error: 'Failed to fetch secret versions' });
    }
});

// Get a specific version of a secret
app.get('/api/secrets/:path/versions/:version', authenticateApiKey, async (req, res) => {
    try {
        const secretPath = decodeURIComponent(req.params.path);
        const version = parseInt(req.params.version);

        if (isNaN(version) || version <= 0) {
            return res.status(400).json({ error: 'Invalid version number' });
        }

        const secretData = await getSecretVersion(`secret/data/${secretPath}`, version);

        const secret = {
            name: secretPath,
            path: secretPath,
            version: version,
            category: secretData.category || 'other',
            description: secretData.description || '',
            data: { ...secretData }
        };

        // Remove metadata fields from data
        delete secret.data.category;
        delete secret.data.description;

        res.json({ secret });
    } catch (error) {
        console.error('Failed to fetch secret version:', error);
        res.status(404).json({ error: 'Secret version not found' });
    }
});

// Import secrets
app.post('/api/import', authenticateApiKey, async (req, res) => {
    try {
        const { secrets } = req.body;

        if (!Array.isArray(secrets)) {
            return res.status(400).json({
                error: 'Invalid import format: secrets must be an array'
            });
        }

        let imported = 0;
        let failed = 0;

        for (const secret of secrets) {
            try {
                const secretData = {
                    ...secret.data,
                    category: secret.category || 'other',
                    description: secret.description || ''
                };

                await setSecret(`secret/data/${secret.name}`, secretData);
                imported++;
            } catch (error) {
                console.error(`Failed to import secret ${secret.name}:`, error);
                failed++;
            }
        }

        res.json({
            message: 'Import completed',
            imported,
            failed
        });
    } catch (error) {
        console.error('Failed to import secrets:', error);
        res.status(500).json({ error: 'Failed to import secrets' });
    }
});


// Health check endpoint
app.get('/health', (req, res) => {
    res.json({ status: 'ok', timestamp: new Date().toISOString() });
});

// Get recent audit logs
app.get('/api/audit-logs', authenticateApiKey, async (req, res) => {
    try {
        // Extract query parameters
        const limit = parseInt(req.query.limit) || 100;
        const { actor, action, resource } = req.query;

        const options = { limit };
        if (actor) options.actor = actor;
        if (action) options.action = action;
        if (resource) options.resource = resource;

        // Log this access to audit logs as well
        await auditLogger.log({
            action: 'read',
            resource: 'audit-logs',
            actor: req.actor,
            details: {
                queryParams: req.query
            }
        });

        const logs = await auditLogger.getRecentLogs(options);
        res.json({
            logs,
            count: logs.length,
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        console.error('Failed to fetch audit logs:', error);
        res.status(500).json({ error: 'Failed to fetch audit logs' });
    }
});


app.get('/webhook-secret', authenticateApiKey, async (req, res) => {
    try {
        const secret = await getSecret('secret/data/webhooks/webhook-secret');
        res.json({ webhookSecret: secret.key });
    } catch (error) {
        res.status(500).json({ error: 'Failed to fetch webhook secret' });
    }
});


app.listen(port, () => {
    console.log(`Vault Config Service running at http://localhost:${port}`);
    console.log(`Web Interface: http://localhost:${port}`);
    console.log(`Using Vault at: ${VAULT_ADDR}`);
    console.log(`API Key required for authentication`);
});
