{"name": "vault-config-service", "version": "2.0.0", "description": "General-purpose configuration and secret management service with web interface", "main": "main.js", "scripts": {"start": "node main.js", "dev": "nodemon main.js"}, "dependencies": {"axios": "^1.3.4", "cors": "^2.8.5", "dotenv": "^16.0.3", "express": "^4.18.2", "helmet": "^7.2.0", "morgan": "^1.10.0"}, "devDependencies": {"nodemon": "^2.0.22"}}