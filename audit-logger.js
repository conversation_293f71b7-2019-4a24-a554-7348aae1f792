/**
 * Audit Logger for the Vault Configuration Service
 * Provides logging functionality for security audit purposes
 */

const fs = require('fs');
const path = require('path');
const { createHash } = require('crypto');

class AuditLogger {
    constructor(options = {}) {
        this.logPath = options.logPath || path.join(__dirname, 'logs');
        this.logFile = options.logFile || path.join(this.logPath, 'audit.log');
        this.maxLogSize = options.maxLogSize || 10 * 1024 * 1024; // 10MB
        this.maxLogFiles = options.maxLogFiles || 5;
        
        // Ensure log directory exists
        if (!fs.existsSync(this.logPath)) {
            fs.mkdirSync(this.logPath, { recursive: true });
        }
    }
    
    /**
     * Log an audit event
     * @param {Object} event - The audit event object
     * @param {string} event.action - The action performed (e.g., 'create', 'read', 'update', 'delete')
     * @param {string} event.resource - The resource acted upon (e.g., 'secret/data/my-secret')
     * @param {string} event.actor - The actor performing the action (e.g., IP address, user ID)
     * @param {Object} event.details - Additional details about the action
     */
    async log(event) {
        if (!event || !event.action || !event.resource || !event.actor) {
            throw new Error('Invalid audit event: action, resource, and actor are required');
        }
        
        const timestamp = new Date().toISOString();
        const id = this.generateEventId(timestamp, event);
        
        const logEntry = {
            id,
            timestamp,
            ...event,
            outcome: event.outcome || 'success'
        };
        
        try {
            await this.rotateLogIfNeeded();
            await this.writeToLog(logEntry);
            return logEntry;
        } catch (error) {
            console.error('Failed to write audit log:', error);
            throw error;
        }
    }
    
    /**
     * Generate a unique event ID
     */
    generateEventId(timestamp, event) {
        const content = `${timestamp}-${event.actor}-${event.action}-${event.resource}`;
        return createHash('sha256').update(content).digest('hex').substring(0, 16);
    }
    
    /**
     * Write entry to log file
     */
    async writeToLog(logEntry) {
        return new Promise((resolve, reject) => {
            // Serialize to JSON, add newline
            const logLine = JSON.stringify(logEntry) + '\n';
            
            fs.appendFile(this.logFile, logLine, 'utf8', (err) => {
                if (err) return reject(err);
                resolve();
            });
        });
    }
    
    /**
     * Rotate log file if needed
     */
    async rotateLogIfNeeded() {
        try {
            // Check if log file exists
            if (!fs.existsSync(this.logFile)) {
                return;
            }
            
            const stats = fs.statSync(this.logFile);
            if (stats.size >= this.maxLogSize) {
                await this.rotateLog();
            }
        } catch (error) {
            console.error('Error checking log size:', error);
        }
    }
    
    /**
     * Rotate log files
     */
    async rotateLog() {
        // First, rotate existing log files
        for (let i = this.maxLogFiles - 1; i >= 1; i--) {
            const oldFile = `${this.logFile}.${i}`;
            const newFile = `${this.logFile}.${i + 1}`;
            
            if (fs.existsSync(oldFile) && i < this.maxLogFiles) {
                try {
                    fs.renameSync(oldFile, newFile);
                } catch (error) {
                    console.error(`Failed to rotate log from ${oldFile} to ${newFile}:`, error);
                }
            }
        }
        
        // Move current log to .1
        if (fs.existsSync(this.logFile)) {
            try {
                fs.renameSync(this.logFile, `${this.logFile}.1`);
            } catch (error) {
                console.error(`Failed to rotate current log:`, error);
            }
        }
    }
    
    /**
     * Get recent audit logs
     * @param {Object} options - Query options
     * @param {number} options.limit - Maximum number of events to return
     * @param {string} options.actor - Filter by actor
     * @param {string} options.action - Filter by action
     * @param {string} options.resource - Filter by resource
     * @returns {Array} - Array of audit log entries
     */
    async getRecentLogs(options = {}) {
        const limit = options.limit || 100;
        const logs = [];
        
        try {
            // Read current log file
            if (fs.existsSync(this.logFile)) {
                const content = fs.readFileSync(this.logFile, 'utf8');
                const lines = content.split('\n').filter(line => line.trim());
                
                for (let i = lines.length - 1; i >= 0 && logs.length < limit; i--) {
                    try {
                        const logEntry = JSON.parse(lines[i]);
                        
                        // Apply filters
                        if (options.actor && logEntry.actor !== options.actor) continue;
                        if (options.action && logEntry.action !== options.action) continue;
                        if (options.resource && !logEntry.resource.includes(options.resource)) continue;
                        
                        logs.push(logEntry);
                    } catch (e) {
                        console.error('Failed to parse log entry:', e);
                    }
                }
            }
            
            return logs;
        } catch (error) {
            console.error('Failed to read audit logs:', error);
            return [];
        }
    }
}

module.exports = AuditLogger;
