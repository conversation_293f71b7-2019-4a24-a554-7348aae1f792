const axios = require("axios");
require("dotenv").config();

const VAULT_API_BASE =
  process.env.API_BASE_URL || "http://localhost:3001/api/secrets";
const API_KEY = process.env.API_KEY || "your-secure-api-key";

const secretsToCreate = [
  {
    name: "webhook-secret",
    category: "security",
    description: "Webhook secret for payload verification",
    data: {
      secret: "oussamaOussama",
    },
  },
  {
    name: "repo-url",
    category: "repository",
    description: "Repository URL",
    data: {
      url: "https://github.com/marwaneRomani/webhook-test.git",
    },
  },
  {
    name: "local-repo-path",
    category: "repository",
    description: "Local path to clone the repo",
    data: {
      path: "/personal_data/test",
    },
  },
  {
    name: "git-hosting-repository-username",
    category: "credentials",
    description: "Git hosting repository username",
    data: {
      username: "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>",
    },
  },
  {
    name: "git-hosting-repository-authToken",
    category: "credentials",
    description: "Git hosting repository authentication token",
    data: {
      authToken: "token",
    },
  },
  {
    name: "registry-url",
    category: "registry",
    description: "Docker registry URL",
    data: {
      url: "http://localhost:5000/",
    },
  },
  {
    name: "rabbit-url",
    category: "messaging",
    description: "RabbitMQ URL",
    data: {
      url: "amqp://guest:guest@localhost:5672/",
    },
  },
  {
    name: "docker-username",
    category: "credentials",
    description: "Docker registry username",
    data: {
      username: "",
    },
  },
  {
    name: "docker-password",
    category: "credentials",
    description: "Docker registry password",
    data: {
      password: "",
    },
  },
  {
    name: "scan-timeout",
    category: "settings",
    description: "Timeout for scanning images in seconds",
    data: {
      timeout: 300,
    },
  },
  {
    name: "retry-count",
    category: "settings",
    description: "Number of retry attempts for operations",
    data: {
      count: 3,
    },
  },
  {
    name: "versioning-strategy",
    category: "settings",
    description:
      "Versioning strategy for builds, e.g., timestamp, semantic versioning",
    data: {
      strategy: "timestamp",
    },
  },
  {
    name: "log-dir",
    category: "logging",
    description: "Directory for logs",
    data: { dir: "logs" },
  },
  {
    name: "log-file",
    category: "logging",
    description: "Log file name",
    data: { file: "builder.log" },
  },
];

async function createSecret(secret) {
  try {
    const response = await axios.post(
      "http://localhost:3001/api/secrets",
      secret,
      {
        headers: {
          "x-api-key": API_KEY,
          "Content-Type": "application/json",
        },
      }
    );

    console.log(`Secret '${secret.name}' created successfully.`);
  } catch (error) {
    console.error(
      `Failed to create secret '${secret.name}':`,
      error.response?.data || error.message
    );
  }
}

async function main() {
  // const res = await axios.get('http://localhost:3001/api/secrets/', {
  //     headers: {
  //         'x-api-key': API_KEY,
  //         'Content-Type': 'application/json'
  //     }
  // })
  // console.log(res.data.secrets)
  for (const secret of secretsToCreate) {
    //secret/data/${name}
    await createSecret(secret);
  }
}

main();
